package com.example.equity.model;

import jakarta.persistence.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@Table(name = "equity_relationship")
public class EquityRelationship {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "parent_id", nullable = false)
    private Company parent;
    
    @ManyToOne
    @JoinColumn(name = "child_id", nullable = false)
    private Company child;
    
    @Column(nullable = false, precision = 10, scale = 6)
    private BigDecimal ratio;
    
    @Column(nullable = false)
    private LocalDate effectiveDate;
    
    private LocalDate endDate;
    
    @Column(nullable = false)
    private boolean isCurrent;
    
    // 构造函数
    public EquityRelationship() {}
    
    public EquityRelationship(Company parent, Company child, BigDecimal ratio) {
        this(parent, child, ratio, LocalDate.now(), null, true);
    }
    
    public EquityRelationship(Company parent, Company child, BigDecimal ratio, 
                             LocalDate effectiveDate, LocalDate endDate, boolean isCurrent) {
        this.parent = parent;
        this.child = child;
        this.ratio = ratio;
        this.effectiveDate = effectiveDate;
        this.endDate = endDate;
        this.isCurrent = isCurrent;
    }
    
    // Getters and setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    public Company getParent() { return parent; }
    public void setParent(Company parent) { this.parent = parent; }
    public Company getChild() { return child; }
    public void setChild(Company child) { this.child = child; }
    public BigDecimal getRatio() { return ratio; }
    public void setRatio(BigDecimal ratio) { this.ratio = ratio; }
    public LocalDate getEffectiveDate() { return effectiveDate; }
    public void setEffectiveDate(LocalDate effectiveDate) { this.effectiveDate = effectiveDate; }
    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
    public boolean isCurrent() { return isCurrent; }
    public void setCurrent(boolean current) { isCurrent = current; }
    
    // 辅助方法（用于计算引擎）
    public Long getParentId() {
        return parent != null ? parent.getId() : null;
    }
    
    public Long getChildId() {
        return child != null ? child.getId() : null;
    }
}
