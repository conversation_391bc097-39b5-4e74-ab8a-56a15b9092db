package com.example.equity.calculation;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 智能股权计算引擎（支持增量计算）
 * 特点：
 * 1. 自动区分增量/全量计算
 * 2. 影响范围分析
 * 3. 异步刷新机制
 * 4. 与数据库无缝集成
 */
public class SmartEquityCalculator {

    // 核心组件
    private final EquityCalculator baseCalculator;
    private final ImpactAnalyzer impactAnalyzer;
    private final DatabaseSaver databaseSaver;
    
    // 性能阈值配置
    private static final int INCREMENTAL_THRESHOLD = 500; // 小于500个受影响项走增量
    private static final int MAX_IMPACT_ANALYSIS = 5000;  // 最大影响分析范围
    
    // 构造函数
    public SmartEquityCalculator(
            List<EquityCalculator.EquityRelationship> currentRelationships,
            int maxDepth,
            DatabaseSaver databaseSaver) {
        
        this.baseCalculator = new EquityCalculator(currentRelationships, maxDepth);
        this.impactAnalyzer = new ImpactAnalyzer(currentRelationships, maxDepth);
        this.databaseSaver = databaseSaver;
    }

    /**
     * 处理股权关系变更（核心入口）
     * 
     * @param added 新增关系
     * @param updated 更新关系
     * @param deleted 删除关系
     * @return 受影响的计算结果数量
     */
    public int handleChanges(
            List<EquityCalculator.EquityRelationship> added,
            List<EquityCalculator.EquityRelationship> updated,
            List<EquityCalculator.EquityRelationship> deleted) {
        
        // 1. 分析影响范围
        Set<CalculationKey> affectedKeys = impactAnalyzer.analyzeImpact(
            added, updated, deleted, MAX_IMPACT_ANALYSIS);
        
        // 2. 智能决策：增量 or 全量
        if (shouldUseIncremental(affectedKeys)) {
            return performIncrementalCalculation(affectedKeys);
        } else {
            return performFullCalculation();
        }
    }
    
    /**
     * 异步处理变更（推荐用于UI交互）
     */
    public CompletableFuture<Integer> handleChangesAsync(
            List<EquityCalculator.EquityRelationship> added,
            List<EquityCalculator.EquityRelationship> updated,
            List<EquityCalculator.EquityRelationship> deleted) {
        
        return CompletableFuture.supplyAsync(() -> 
            handleChanges(added, updated, deleted));
    }
    
    // ====================== 决策逻辑 ======================
    
    private boolean shouldUseIncremental(Set<CalculationKey> affectedKeys) {
        return affectedKeys != null && 
               !affectedKeys.isEmpty() && 
               affectedKeys.size() <= INCREMENTAL_THRESHOLD;
    }
    
    // ====================== 增量计算 ======================
    
    private int performIncrementalCalculation(Set<CalculationKey> affectedKeys) {
        List<HoldingResult> resultsToSave = new ArrayList<>(affectedKeys.size());
        
        for (CalculationKey key : affectedKeys) {
            BigDecimal ratio = baseCalculator.calculateIndirectHolding(
                key.getParentId(), key.getChildId());
            
            resultsToSave.add(new HoldingResult(
                key.getParentId(), 
                key.getChildId(), 
                ratio
            ));
        }
        
        // 保存到数据库
        databaseSaver.saveResults(resultsToSave);
        return resultsToSave.size();
    }
    
    // ====================== 全量计算 ======================
    
    private int performFullCalculation() {
        // 1. 获取所有公司ID
        Set<Long> allCompanyIds = impactAnalyzer.getAllCompanyIds();
        List<Long> companyList = new ArrayList<>(allCompanyIds);
        
        // 2. 预计算所有结果（跳过自身）
        List<HoldingResult> results = new ArrayList<>();
        for (int i = 0; i < companyList.size(); i++) {
            Long parent = companyList.get(i);
            for (int j = 0; j < companyList.size(); j++) {
                if (i == j) continue; // 跳过自身
                
                Long child = companyList.get(j);
                BigDecimal ratio = baseCalculator.calculateIndirectHolding(parent, child);
                results.add(new HoldingResult(parent, child, ratio));
            }
        }
        
        // 3. 保存到数据库
        databaseSaver.saveResults(results);
        return results.size();
    }
    
    // ====================== 内部类 ======================
    
    /**
     * 影响分析器（核心组件）
     */
    private static class ImpactAnalyzer {
        private final Set<Long> allCompanyIds;
        private final Map<Long, Set<Long>> upstreamMap;  // 子公司->母公司集合
        private final Map<Long, Set<Long>> downstreamMap; // 母公司->子公司集合
        private final int maxDepth;
        
        public ImpactAnalyzer(List<EquityCalculator.EquityRelationship> relationships, int maxDepth) {
            this.maxDepth = maxDepth;
            this.allCompanyIds = new HashSet<>();
            this.upstreamMap = new HashMap<>();
            this.downstreamMap = new HashMap<>();
            
            // 构建影响关系图
            buildImpactGraph(relationships);
        }
        
        private void buildImpactGraph(List<EquityCalculator.EquityRelationship> relationships) {
            // 收集所有公司ID
            relationships.forEach(rel -> {
                allCompanyIds.add(rel.getParentId());
                allCompanyIds.add(rel.getChildId());
            });

            // 构建上游（谁控制我）
            relationships.forEach(rel -> {
                upstreamMap.computeIfAbsent(rel.getChildId(), k -> new HashSet<>())
                          .add(rel.getParentId());
            });

            // 构建下游（我控制谁）
            relationships.forEach(rel -> {
                downstreamMap.computeIfAbsent(rel.getParentId(), k -> new HashSet<>())
                           .add(rel.getChildId());
            });
        }

        /**
         * 分析变更的影响范围
         *
         * @param added 新增关系
         * @param updated 更新关系
         * @param deleted 删除关系
         * @param maxImpact 最大影响范围（超过则返回null）
         * @return 受影响的计算结果键集合
         */
        public Set<CalculationKey> analyzeImpact(
                List<EquityCalculator.EquityRelationship> added,
                List<EquityCalculator.EquityRelationship> updated,
                List<EquityCalculator.EquityRelationship> deleted,
                int maxImpact) {

            Set<CalculationKey> affected = new HashSet<>();

            // 处理删除的关系
            for (EquityCalculator.EquityRelationship rel : deleted) {
                expandImpact(affected, rel.getParentId(), rel.getChildId(), maxImpact);
                if (exceedsThreshold(affected, maxImpact)) return null;
            }

            // 处理更新的关系（视为先删除后添加）
            for (EquityCalculator.EquityRelationship rel : updated) {
                expandImpact(affected, rel.getParentId(), rel.getChildId(), maxImpact);
                if (exceedsThreshold(affected, maxImpact)) return null;
            }

            // 处理新增的关系
            for (EquityCalculator.EquityRelationship rel : added) {
                expandImpact(affected, rel.getParentId(), rel.getChildId(), maxImpact);
                if (exceedsThreshold(affected, maxImpact)) return null;
            }

            return affected;
        }

        /**
         * 扩展影响范围（核心算法）
         */
        private void expandImpact(
                Set<CalculationKey> affected,
                Long parent,
                Long child,
                int maxImpact) {

            // 1. 直接影响：包含此关系的所有计算
            addDirectImpacts(affected, parent, child);
            if (exceedsThreshold(affected, maxImpact)) return;

            // 2. 间接影响：上游影响（谁受parent影响）
            Set<Long> upstreamParents = findUpstream(parent, maxDepth);
            for (Long upstreamParent : upstreamParents) {
                affected.add(new CalculationKey(upstreamParent, child));
                if (exceedsThreshold(affected, maxImpact)) return;
            }

            // 3. 间接影响：下游影响（谁受child影响）
            Set<Long> downstreamChildren = findDownstream(child, maxDepth);
            for (Long downstreamChild : downstreamChildren) {
                affected.add(new CalculationKey(parent, downstreamChild));
                if (exceedsThreshold(affected, maxImpact)) return;
            }

            // 4. 交叉影响（上游对下游）
            for (Long upstreamParent : upstreamParents) {
                for (Long downstreamChild : downstreamChildren) {
                    affected.add(new CalculationKey(upstreamParent, downstreamChild));
                    if (exceedsThreshold(affected, maxImpact)) return;
                }
            }
        }

        private void addDirectImpacts(Set<CalculationKey> affected, Long parent, Long child) {
            // 1. 直接影响：parent->child
            affected.add(new CalculationKey(parent, child));

            // 2. 间接影响：谁通过parent控制child
            Set<Long> upstreamParents = findUpstream(parent, 1);
            for (Long upstreamParent : upstreamParents) {
                affected.add(new CalculationKey(upstreamParent, child));
            }

            // 3. 间接影响：parent通过child控制谁
            Set<Long> downstreamChildren = findDownstream(child, 1);
            for (Long downstreamChild : downstreamChildren) {
                affected.add(new CalculationKey(parent, downstreamChild));
            }
        }

        private boolean exceedsThreshold(Set<CalculationKey> affected, int maxImpact) {
            return affected.size() > maxImpact;
        }

        /**
         * 查找上游公司（谁控制我）
         */
        private Set<Long> findUpstream(Long companyId, int maxDepth) {
            Set<Long> result = new HashSet<>();
            Queue<UpstreamNode> queue = new LinkedList<>();
            queue.offer(new UpstreamNode(companyId, 0));

            while (!queue.isEmpty()) {
                UpstreamNode current = queue.poll();
                if (current.depth >= maxDepth) continue;

                Set<Long> parents = upstreamMap.getOrDefault(current.companyId, Collections.emptySet());
                for (Long parent : parents) {
                    if (result.add(parent)) {
                        queue.offer(new UpstreamNode(parent, current.depth + 1));
                    }
                }
            }

            return result;
        }

        /**
         * 查找下游公司（我控制谁）
         */
        private Set<Long> findDownstream(Long companyId, int maxDepth) {
            Set<Long> result = new HashSet<>();
            Queue<DownstreamNode> queue = new LinkedList<>();
            queue.offer(new DownstreamNode(companyId, 0));

            while (!queue.isEmpty()) {
                DownstreamNode current = queue.poll();
                if (current.depth >= maxDepth) continue;

                Set<Long> children = downstreamMap.getOrDefault(current.companyId, Collections.emptySet());
                for (Long child : children) {
                    if (result.add(child)) {
                        queue.offer(new DownstreamNode(child, current.depth + 1));
                    }
                }
            }

            return result;
        }

        public Set<Long> getAllCompanyIds() {
            return Collections.unmodifiableSet(allCompanyIds);
        }

        // 内部节点类
        private static class UpstreamNode {
            final Long companyId;
            final int depth;

            UpstreamNode(Long companyId, int depth) {
                this.companyId = companyId;
                this.depth = depth;
            }
        }

        private static class DownstreamNode {
            final Long companyId;
            final int depth;

            DownstreamNode(Long companyId, int depth) {
                this.companyId = companyId;
                this.depth = depth;
            }
        }
    }

    // ====================== 辅助类 ======================

    /**
     * 计算结果键（数据库唯一标识）
     */
    public static class CalculationKey {
        private final Long parentId;
        private final Long childId;

        public CalculationKey(Long parentId, Long childId) {
            this.parentId = parentId;
            this.childId = childId;
        }

        public Long getParentId() { return parentId; }
        public Long getChildId() { return childId; }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            CalculationKey that = (CalculationKey) o;
            return parentId.equals(that.parentId) && childId.equals(that.childId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(parentId, childId);
        }
    }

    /**
     * 持股结果（简化版，用于计算引擎）
     */
    public static class HoldingResult {
        private final Long parentId;
        private final Long childId;
        private final BigDecimal ratio;

        public HoldingResult(Long parentId, Long childId, BigDecimal ratio) {
            this.parentId = parentId;
            this.childId = childId;
            this.ratio = ratio;
        }

        public Long getParentId() { return parentId; }
        public Long getChildId() { return childId; }
        public BigDecimal getRatio() { return ratio; }
    }

    /**
     * 数据库保存器（接口）
     */
    public interface DatabaseSaver {
        void saveResults(List<HoldingResult> results);
    }
}
