-- 创建测试公司
INSERT INTO company (name, code) VALUES 
('集团总部', 'GROUP'),
('子公司A', 'SUB_A'),
('子公司B', 'SUB_B'),
('子公司C', 'SUB_C'),
('子公司D', 'SUB_D');

-- 创建股权关系
-- 集团→子公司A (60%)
INSERT INTO equity_relationship (parent_id, child_id, ratio, is_current) 
VALUES (1, 2, 0.6, true);

-- 子公司A→子公司B (50%)
INSERT INTO equity_relationship (parent_id, child_id, ratio, is_current) 
VALUES (2, 3, 0.5, true);

-- 集团→子公司C (40%)
INSERT INTO equity_relationship (parent_id, child_id, ratio, is_current) 
VALUES (1, 4, 0.4, true);

-- 子公司C→子公司B (30%)
INSERT INTO equity_relationship (parent_id, child_id, ratio, is_current) 
VALUES (4, 3, 0.3, true);

-- 子公司B→子公司D (70%)
INSERT INTO equity_relationship (parent_id, child_id, ratio, is_current) 
VALUES (3, 5, 0.7, true);
