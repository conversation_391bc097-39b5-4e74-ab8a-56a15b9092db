-- 创建公司表
CREATE TABLE company (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL
);

-- 创建股权关系表
CREATE TABLE equity_relationship (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    parent_id BIGINT NOT NULL,
    child_id BIGINT NOT NULL,
    ratio DECIMAL(10,6) NOT NULL CHECK (ratio >= 0 AND ratio <= 1),
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    end_date DATE,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_parent FOREIGN KEY (parent_id) REFERENCES company(id),
    CONSTRAINT fk_child FOREIGN KEY (child_id) REFERENCES company(id)
);

CREATE INDEX idx_equity_parent ON equity_relationship(parent_id);
CREATE INDEX idx_equity_child ON equity_relationship(child_id);
CREATE INDEX idx_equity_current ON equity_relationship(is_current);

-- 创建持股结果表
CREATE TABLE holding_result (
    parent_id BIGINT NOT NULL,
    child_id BIGINT NOT NULL,
    indirect_ratio DECIMAL(10,6) NOT NULL,
    calculation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (parent_id, child_id),
    
    CONSTRAINT fk_hr_parent FOREIGN KEY (parent_id) REFERENCES company(id),
    CONSTRAINT fk_hr_child FOREIGN KEY (child_id) REFERENCES company(id)
);

CREATE INDEX idx_holding_ratio ON holding_result(indirect_ratio);
